# Volume Persistence Guide

Your Discord music bot now remembers the volume level for each server! The bot will automatically restore the last used volume when it rejoins a voice channel.

## How It Works

### 🔄 **Automatic Volume Persistence**
- When you change the volume using `/volume`, it's automatically saved for your server
- When the bot leaves and rejoins a voice channel, it uses the saved volume
- Each server has its own independent volume setting
- Default volume for new servers is 80%

### 💾 **When Volume is Saved**
1. **Manual Volume Changes**: When you use `/volume <level>`
2. **Bot Disconnect**: When the bot leaves the voice channel (automatically saves current volume)
3. **Server Settings**: Volume is stored per-server in `src/data/guildSettings.json`

## Commands

### `/volume` - Check Current Volume
```
/volume
```
Shows:
- **Saved Volume**: The volume that will be used when bot rejoins
- **Current Volume**: The current active volume (if playing)
- **Status**: Whether music is currently playing

### `/volume <level>` - Set Volume
```
/volume level: 150
```
- Sets volume to 150% and saves it for this server
- Works even when no music is playing (saves for next time)
- Range: 0-250%

## Examples

### Scenario 1: Setting Volume While Playing
```
1. <PERSON><PERSON> is playing music at 80%
2. You run: /volume level: 120
3. ✅ Volume immediately changes to 120%
4. ✅ 120% is saved for this server
5. When bot rejoins later, it will use 120%
```

### Scenario 2: Setting Volume When Not Playing
```
1. No music is playing
2. You run: /volume level: 200
3. ✅ 200% is saved for this server
4. Next time bot plays music, it will start at 200%
```

### Scenario 3: Checking Volume Settings
```
1. You run: /volume
2. Shows:
   - Saved Volume: 150%
   - Current Volume: No active queue
   - Status: ⏸️ Not playing
```

## Technical Details

### 🗂️ **Storage System**
- Volume settings are stored in `src/data/guildSettings.json`
- Each server (guild) has its own settings object
- Settings include volume level and last updated timestamp
- File is automatically created when first volume is set

### 🔧 **Queue Creation**
- All music commands (`/play`, `/playlist`) use saved volume when creating queues
- Volume is applied immediately when the queue is created
- No delay or volume adjustment after joining

### 🛡️ **Error Handling**
- If settings file is corrupted, defaults to 80% volume
- If volume data is missing for a server, creates new default settings
- Graceful fallback to default volume if any errors occur

## Benefits

### ✅ **User Experience**
- **Consistent Volume**: No need to readjust volume every time
- **Per-Server Settings**: Different servers can have different preferred volumes
- **Set and Forget**: Configure once, works automatically
- **No Interruption**: Volume is set before music starts playing

### ✅ **Server Management**
- **Independent Settings**: Each server maintains its own volume preference
- **Persistent Storage**: Settings survive bot restarts
- **Automatic Cleanup**: Old unused settings can be cleaned up automatically

## Troubleshooting

### Volume Not Persisting
1. Check if bot has write permissions in the bot directory
2. Verify `src/data/` directory exists and is writable
3. Look for error messages in bot console logs

### Volume Resets to Default
- This happens for new servers or if settings file is missing
- Use `/volume <level>` to set your preferred volume
- Check console logs for any storage errors

### Different Volume Than Expected
1. Use `/volume` to check saved vs current volume
2. Verify you're setting volume in the correct server
3. Remember each server has independent settings

## Advanced Features

### 🧹 **Automatic Cleanup**
The bot can automatically clean up old guild settings (disabled by default):
- Removes settings for servers not used in 30+ days
- Helps keep storage file size manageable
- Can be enabled in the guild settings utility

### 📊 **Volume Information**
The enhanced `/volume` command shows:
- What volume is saved for your server
- Current active volume (if music is playing)
- Whether the bot is currently playing music
- Helpful status indicators

## File Structure

```
src/
├── data/
│   └── guildSettings.json     # Volume settings storage
├── utils/
│   ├── guildSettings.js       # Settings management
│   └── queueManager.js        # Queue creation with saved settings
└── commands/music/
    ├── volume.js              # Enhanced volume command
    ├── play.js                # Uses saved volume
    └── playlist.js            # Uses saved volume
```

Enjoy your persistent volume settings! 🎵
