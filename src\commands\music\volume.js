const { SlashCommandBuilder } = require('@discordjs/builders');
const { EmbedBuilder } = require('discord.js');
const guildSettings = require('../../utils/guildSettings');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('volume')
        .setDescription('Adjust or check the volume of the music player')
        .addIntegerOption(option =>
            option.setName('level')
                .setDescription('Volume level (0-250%) - leave empty to check current volume')
                .setMinValue(0)
                .setMaxValue(250)
                .setRequired(false)
        ),
    async execute(interaction, client) {
        await interaction.deferReply();

        try {
            const queue = client.player.nodes.get(interaction.guildId);
            const volumeLevel = interaction.options.getInteger('level');

            // If no volume level provided, show current volume
            if (volumeLevel === null) {
                const savedVolume = guildSettings.getVolume(interaction.guildId);
                const currentVolume = queue?.node?.volume;

                const embed = new EmbedBuilder()
                    .setColor(0x3498DB)
                    .setTitle('🔊 Volume Information')
                    .addFields(
                        { name: 'Saved Volume', value: `${savedVolume}%`, inline: true },
                        { name: 'Current Volume', value: currentVolume ? `${currentVolume}%` : 'No active queue', inline: true },
                        { name: 'Status', value: queue?.isPlaying() ? '🎵 Playing' : '⏸️ Not playing', inline: true }
                    )
                    .setDescription(`The bot will use **${savedVolume}%** volume when it next joins a voice channel.`)
                    .setFooter({ text: `Requested by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL() });

                return interaction.editReply({ embeds: [embed] });
            }

            // Check if the player exists and is playing for volume adjustment
            if (!queue || !queue.isPlaying()) {
                // Save the volume even if not playing
                guildSettings.setVolume(interaction.guildId, volumeLevel);

                const embed = new EmbedBuilder()
                    .setColor(0x3498DB)
                    .setTitle('🔊 Volume Saved')
                    .setDescription(`Volume set to **${volumeLevel}%** and saved for this server.\nThe bot will use this volume when it next plays music.`)
                    .setFooter({ text: `Requested by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL() });

                return interaction.editReply({ embeds: [embed] });
            }

            // Set the volume on active queue
            queue.node.setVolume(volumeLevel);

            // Save the volume setting for this guild
            guildSettings.setVolume(interaction.guildId, volumeLevel);

            // Create a visual representation of the volume level
            const volumeBar = createVolumeBar(volumeLevel);

            // Create an embed to display the volume change
            const embed = new EmbedBuilder()
                .setColor(0x3498DB)
                .setTitle('🔊 Volume Adjusted')
                .setDescription(`Volume set to **${volumeLevel}%** and saved for this server.\n\n${volumeBar}`)
                .setFooter({ text: `Requested by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL() });

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in volume command:', error);
            await interaction.editReply(`❌ | Error: ${error.message}`);
        }
    },
};

/**
 * Creates a visual representation of the volume level
 * @param {number} volume - The volume level (0-250)
 * @returns {string} - A string representing the volume bar
 */
function createVolumeBar(volume) {
    // Normalize volume to a 0-20 scale for the bar
    const normalizedVolume = Math.round((volume / 250) * 20);

    // Create the filled part of the bar
    const filledBars = '█'.repeat(normalizedVolume);

    // Create the empty part of the bar
    const emptyBars = '░'.repeat(20 - normalizedVolume);

    // Determine the emoji based on volume level
    let emoji;
    if (volume === 0) {
        emoji = '🔇';
    } else if (volume < 50) {
        emoji = '🔈';
    } else if (volume < 150) {
        emoji = '🔉';
    } else {
        emoji = '🔊';
    }

    // Return the formatted volume bar
    return `${emoji} ${filledBars}${emptyBars} ${volume}%`;
}
