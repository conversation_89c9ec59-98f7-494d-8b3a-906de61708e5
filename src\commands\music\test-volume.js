// File: src/commands/music/test-volume.js
// Test command to verify volume persistence functionality

const { SlashCommandBuilder } = require('@discordjs/builders');
const { EmbedBuilder } = require('discord.js');
const guildSettings = require('../../utils/guildSettings');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('test-volume')
        .setDescription('Test volume persistence functionality (admin only)')
        .addSubcommand(subcommand =>
            subcommand
                .setName('set')
                .setDescription('Set a test volume')
                .addIntegerOption(option =>
                    option.setName('volume')
                        .setDescription('Test volume level')
                        .setMinValue(0)
                        .setMaxValue(250)
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('get')
                .setDescription('Get current saved volume')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Reset volume to default (80%)')
        ),
    async execute(interaction, client) {
        await interaction.deferReply();

        // Check if user has admin permissions
        if (!interaction.member.permissions.has('Administrator')) {
            return interaction.editReply({ 
                content: '❌ | This command requires Administrator permissions!', 
                ephemeral: true 
            });
        }

        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'set': {
                    const volume = interaction.options.getInteger('volume');
                    guildSettings.setVolume(interaction.guildId, volume);
                    
                    const embed = new EmbedBuilder()
                        .setColor(0x00FF00)
                        .setTitle('✅ Test Volume Set')
                        .setDescription(`Test volume set to **${volume}%** for this server.`)
                        .addFields(
                            { name: 'Guild ID', value: interaction.guildId, inline: true },
                            { name: 'Set By', value: interaction.user.tag, inline: true }
                        )
                        .setTimestamp();
                    
                    await interaction.editReply({ embeds: [embed] });
                    break;
                }

                case 'get': {
                    const savedVolume = guildSettings.getVolume(interaction.guildId);
                    const allSettings = guildSettings.getAllSettings();
                    const guildCount = allSettings.size;
                    
                    const embed = new EmbedBuilder()
                        .setColor(0x3498DB)
                        .setTitle('📊 Volume Test Results')
                        .setDescription(`Current saved volume for this server: **${savedVolume}%**`)
                        .addFields(
                            { name: 'Guild ID', value: interaction.guildId, inline: true },
                            { name: 'Total Guilds with Settings', value: guildCount.toString(), inline: true },
                            { name: 'Default Volume', value: '80%', inline: true }
                        )
                        .setTimestamp();
                    
                    await interaction.editReply({ embeds: [embed] });
                    break;
                }

                case 'reset': {
                    guildSettings.setVolume(interaction.guildId, 80);
                    
                    const embed = new EmbedBuilder()
                        .setColor(0xFFA500)
                        .setTitle('🔄 Volume Reset')
                        .setDescription('Volume has been reset to default **80%** for this server.')
                        .addFields(
                            { name: 'Guild ID', value: interaction.guildId, inline: true },
                            { name: 'Reset By', value: interaction.user.tag, inline: true }
                        )
                        .setTimestamp();
                    
                    await interaction.editReply({ embeds: [embed] });
                    break;
                }
            }
        } catch (error) {
            console.error('Error in test-volume command:', error);
            await interaction.editReply({
                content: `❌ | Error testing volume: ${error.message}`,
                ephemeral: true
            });
        }
    },
};
