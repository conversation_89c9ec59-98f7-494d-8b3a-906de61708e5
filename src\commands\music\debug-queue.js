// File: src/commands/music/debug-queue.js
// Debug command to check queue status and volume settings

const { SlashCommandBuilder } = require('@discordjs/builders');
const { EmbedBuilder } = require('discord.js');
const guildSettings = require('../../utils/guildSettings');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('debug-queue')
        .setDescription('Debug queue and volume information (admin only)'),
    async execute(interaction, client) {
        await interaction.deferReply();

        // Check if user has admin permissions
        if (!interaction.member.permissions.has('Administrator')) {
            return interaction.editReply({ 
                content: '❌ | This command requires Administrator permissions!', 
                ephemeral: true 
            });
        }

        try {
            const queue = client.player.nodes.get(interaction.guildId);
            const savedVolume = guildSettings.getVolume(interaction.guildId);

            const embed = new EmbedBuilder()
                .setColor(0x3498DB)
                .setTitle('🔧 Queue Debug Information')
                .setTimestamp();

            // Basic queue info
            if (!queue) {
                embed.setDescription('❌ No active queue found')
                    .addFields(
                        { name: 'Saved Volume', value: `${savedVolume}%`, inline: true },
                        { name: 'Queue Status', value: 'Not created', inline: true },
                        { name: 'Connection', value: 'None', inline: true }
                    );
            } else {
                // Detailed queue information
                const isPlaying = queue.isPlaying();
                const isPaused = queue.node.isPaused();
                const currentVolume = queue.node.volume;
                const tracksCount = queue.tracks.size;
                const currentTrack = queue.currentTrack;

                embed.setDescription('✅ Active queue found')
                    .addFields(
                        { name: 'Saved Volume', value: `${savedVolume}%`, inline: true },
                        { name: 'Current Volume', value: `${currentVolume}%`, inline: true },
                        { name: 'Volume Match', value: savedVolume === currentVolume ? '✅ Yes' : '❌ No', inline: true },
                        { name: 'Is Playing', value: isPlaying ? '✅ Yes' : '❌ No', inline: true },
                        { name: 'Is Paused', value: isPaused ? '⏸️ Yes' : '▶️ No', inline: true },
                        { name: 'Tracks in Queue', value: tracksCount.toString(), inline: true },
                        { name: 'Connection Status', value: queue.connection ? '✅ Connected' : '❌ Disconnected', inline: true },
                        { name: 'Volume Disabled', value: queue.options?.disableVolume ? '❌ Yes' : '✅ No', inline: true },
                        { name: 'Node Status', value: queue.node ? '✅ Available' : '❌ Missing', inline: true }
                    );

                if (currentTrack) {
                    embed.addFields({
                        name: 'Current Track',
                        value: `[${currentTrack.title}](${currentTrack.url})`,
                        inline: false
                    });
                }

                // Voice channel info
                if (queue.connection && queue.connection.joinConfig) {
                    const channelId = queue.connection.joinConfig.channelId;
                    const channel = interaction.guild.channels.cache.get(channelId);
                    embed.addFields({
                        name: 'Voice Channel',
                        value: channel ? channel.name : `Unknown (${channelId})`,
                        inline: true
                    });
                }

                // Additional debug info
                if (queue.node) {
                    embed.addFields(
                        { name: 'Stream Dispatcher', value: queue.dispatcher ? '✅ Active' : '❌ Inactive', inline: true },
                        { name: 'Audio Resource', value: queue.node.audioResource ? '✅ Available' : '❌ Missing', inline: true }
                    );
                }
            }

            // Guild settings info
            const allSettings = guildSettings.getAllSettings();
            embed.addFields(
                { name: 'Guild ID', value: interaction.guildId, inline: true },
                { name: 'Total Guilds with Settings', value: allSettings.size.toString(), inline: true }
            );

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in debug-queue command:', error);
            await interaction.editReply({
                content: `❌ | Error getting debug info: ${error.message}`,
                ephemeral: true
            });
        }
    },
};
