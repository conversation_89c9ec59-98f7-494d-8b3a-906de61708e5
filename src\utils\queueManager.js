// File: src/utils/queueManager.js
// Utility functions for managing music queues with persistent settings

const guildSettings = require('./guildSettings');

/**
 * Create a music queue with saved guild settings
 * @param {Player} player - The discord-player instance
 * @param {Guild} guild - The Discord guild
 * @param {Object} options - Queue creation options
 * @param {Object} options.metadata - Queue metadata
 * @param {Object} [overrides] - Optional overrides for queue settings
 * @returns {GuildQueue} The created queue
 */
function createQueueWithSettings(player, guild, options = {}, overrides = {}) {
    // Get saved volume for this guild
    const savedVolume = guildSettings.getVolume(guild.id);

    // Default queue configuration with saved settings
    const defaultConfig = {
        metadata: options.metadata || null,
        selfDeaf: true,
        volume: savedVolume, // Use saved volume
        leaveOnEmpty: true,
        leaveOnEmptyCooldown: 300000, // 5 minutes
        leaveOnEnd: true,
        leaveOnEndCooldown: 300000, // 5 minutes
        bufferingTimeout: 3000, // 3 second buffer
        connectionTimeout: 20000, // 20 second connection timeout
        disableVolume: false, // Keep volume control enabled
        // Ensure volume transformer is available
        disableEqualizer: false,
        disableFilterer: false,
        disableBiquad: false,
        disableResampler: false,
        // Add any additional options passed in
        ...options,
        // Apply any overrides (these take highest priority)
        ...overrides
    };

    // Create the queue
    const queue = player.nodes.create(guild, defaultConfig);

    console.log(`[QueueManager] Created queue with saved volume: ${savedVolume}% for guild ${guild.name}`);

    return queue;
}

/**
 * Get or create a queue with saved settings
 * @param {Player} player - The discord-player instance
 * @param {Guild} guild - The Discord guild
 * @param {Object} options - Queue creation options (only used if queue doesn't exist)
 * @param {Object} [overrides] - Optional overrides for queue settings
 * @returns {GuildQueue} The existing or newly created queue
 */
function getOrCreateQueue(player, guild, options = {}, overrides = {}) {
    // Try to get existing queue first
    let queue = player.nodes.get(guild.id);

    if (!queue) {
        // Create new queue with saved settings
        queue = createQueueWithSettings(player, guild, options, overrides);
    } else {
        console.log(`[QueueManager] Using existing queue for guild ${guild.name}`);
    }

    return queue;
}

/**
 * Save current queue volume to persistent storage
 * @param {GuildQueue} queue - The guild queue
 */
function saveQueueVolume(queue) {
    if (queue && queue.node && typeof queue.node.volume === 'number') {
        guildSettings.setVolume(queue.guild.id, queue.node.volume);
        console.log(`[QueueManager] Saved volume ${queue.node.volume}% for guild ${queue.guild.name}`);
    }
}

/**
 * Restore saved volume to an existing queue
 * @param {GuildQueue} queue - The guild queue
 * @returns {boolean} True if volume was restored, false otherwise
 */
function restoreQueueVolume(queue) {
    if (!queue || !queue.node) {
        return false;
    }

    const savedVolume = guildSettings.getVolume(queue.guild.id);
    const currentVolume = queue.node.volume;

    // Only update if the saved volume is different from current
    if (savedVolume !== currentVolume) {
        queue.node.setVolume(savedVolume);
        console.log(`[QueueManager] Restored volume from ${currentVolume}% to ${savedVolume}% for guild ${queue.guild.name}`);
        return true;
    }

    return false;
}

module.exports = {
    createQueueWithSettings,
    getOrCreateQueue,
    saveQueueVolume,
    restoreQueueVolume
};
