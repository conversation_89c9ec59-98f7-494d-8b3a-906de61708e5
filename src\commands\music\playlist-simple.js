// File: src/commands/music/playlist-simple.js
// Simplified playlist command for testing

const { SlashCommandBuilder } = require('@discordjs/builders');
const { QueryType } = require('discord-player');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('playlist-simple')
        .setDescription('Simple playlist command for testing.')
        .addStringOption(option =>
            option.setName('url')
                .setDescription('YouTube playlist URL')
                .setRequired(true)),
    async execute(interaction, client) {
        await interaction.deferReply();

        try {
            const member = interaction.member;
            if (!member.voice.channel) {
                return interaction.editReply({ content: 'You need to be in a voice channel!', ephemeral: true });
            }

            const playlistUrl = interaction.options.getString('url');
            console.log(`[Playlist-Simple] Loading: ${playlistUrl}`);

            // Search for the playlist
            const searchResult = await client.player.search(playlistUrl, {
                requestedBy: interaction.user,
                searchEngine: QueryType.YOUTUBE_PLAYLIST
            });

            if (!searchResult || !searchResult.tracks || searchResult.tracks.length === 0) {
                return interaction.editReply({ content: '❌ | Could not load playlist!' });
            }

            console.log(`[Playlist-Simple] Found ${searchResult.tracks.length} tracks`);

            // Create queue
            const queue = client.player.nodes.create(interaction.guild, {
                metadata: {
                    channel: interaction.channel,
                    client: interaction.guild.members.me,
                    requestedBy: interaction.user,
                },
                selfDeaf: true,
                volume: 80,
                leaveOnEmpty: true,
                leaveOnEmptyCooldown: 300000,
                leaveOnEnd: true,
                leaveOnEndCooldown: 300000,
                disableVolume: false
            });

            // Connect to voice channel
            if (!queue.connection) {
                await queue.connect(member.voice.channel);
            }

            // Add tracks and play
            queue.addTrack(searchResult.tracks);
            if (!queue.isPlaying()) {
                await queue.node.play();
            }

            // Simple response
            await interaction.editReply({
                content: `✅ Added ${searchResult.tracks.length} tracks from playlist: **${searchResult.playlist?.title || 'Unknown'}**`
            });

            console.log(`[Playlist-Simple] Success!`);

        } catch (error) {
            console.error('[Playlist-Simple] Error:', error);
            await interaction.editReply({
                content: `❌ Error: ${error.message}`
            }).catch(console.error);
        }
    },
};
