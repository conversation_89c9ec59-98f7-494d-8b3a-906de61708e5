// File: src/commands/music/play.js
// Example "play" command.

const { SlashCommandBuilder } = require('@discordjs/builders');
const { EmbedBuilder } = require('discord.js');
const { QueryType } = require('discord-player');
const { createQueueWithSettings } = require('../../utils/queueManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('play')
        .setDescription('Plays a song or adds it to the queue.')
        .addStringOption(option =>
            option.setName('query')
                .setDescription('Song name, URL, or YouTube playlist URL')
                .setRequired(true)),
    async execute(interaction, client) {
        await interaction.deferReply();

        const member = interaction.member;
        if (!member.voice.channel) {
            return interaction.editReply({ content: 'You need to be in a voice channel to play music!', ephemeral: true });
        }

        const query = interaction.options.getString('query');

        try {
            // Determine if the query is a URL and what type
            const isURL = query.match(/^https?:\/\//i);
            const isSoundCloud = query.includes('soundcloud.com');
            const isSpotify = query.includes('spotify.com');
            const isYouTube = query.includes('youtube.com') || query.includes('youtu.be');

            // Check if it's a YouTube playlist URL
            const isYouTubePlaylist = isYouTube && (query.includes('list=') || query.includes('playlist?'));

            // Set appropriate search engine based on URL type
            let searchEngine = QueryType.AUTO;
            if (isURL) {
                if (isSoundCloud) searchEngine = QueryType.SOUNDCLOUD_SEARCH;
                else if (isSpotify) searchEngine = QueryType.SPOTIFY_SEARCH;
                else if (isYouTubePlaylist) searchEngine = QueryType.YOUTUBE_PLAYLIST;
                else if (isYouTube) searchEngine = QueryType.YOUTUBE_SEARCH;
            }

            // Log the search attempt
            console.log(`[Player] Searching for "${query}" using ${searchEngine} engine`);

            // Perform the search
            const searchResult = await client.player.search(query, {
                requestedBy: interaction.user,
                searchEngine: searchEngine
            });

            console.log(`[Player] Search result: ${searchResult ? `Found ${searchResult.tracks.length} tracks` : 'No results'}`);

            if (!searchResult || !searchResult.tracks || searchResult.tracks.length === 0) {
                // Provide more specific error messages based on the source
                if (isSoundCloud) {
                    return interaction.editReply({
                        content: `❌ | No tracks found on SoundCloud for "${query}"! Make sure the SoundCloud extractor is properly loaded.`,
                        ephemeral: true
                    });
                } else if (isSpotify) {
                    return interaction.editReply({
                        content: `❌ | No tracks found on Spotify for "${query}"! Make sure the Spotify extractor is properly loaded and API credentials are set.`,
                        ephemeral: true
                    });
                } else if (isYouTube) {
                    const errorMsg = isYouTubePlaylist
                        ? `❌ | Could not load YouTube playlist "${query}"! Make sure the playlist is public and the URL is correct.`
                        : `❌ | No tracks found on YouTube for "${query}"! Make sure the YouTube extractor is properly loaded.`;
                    return interaction.editReply({
                        content: errorMsg,
                        ephemeral: true
                    });
                } else {
                    return interaction.editReply({
                        content: `❌ | No tracks found for "${query}"! Try a different search term or URL. Use /diagnose to check player status.`,
                        ephemeral: true
                    });
                }
            }

            // Create queue with saved settings
            const queue = createQueueWithSettings(client.player, interaction.guild, {
                metadata: {
                    channel: interaction.channel,
                    client: interaction.guild.members.me,
                    requestedBy: interaction.user,
                }
            });

            // Try to connect to the voice channel
            if (!queue.connection) {
                try {
                    await queue.connect(member.voice.channel);
                } catch (e) {
                    console.error("Failed to connect to voice channel:", e);
                    client.player.nodes.delete(interaction.guildId);
                    return interaction.editReply({ content: '❌ | Could not join your voice channel!', ephemeral: true });
                }
            }

            // Add track(s) to the queue
            if (searchResult.playlist) {
                queue.addTrack(searchResult.tracks);
                if (!queue.isPlaying()) await queue.node.play();

                // Create a rich embed for playlist
                const playlistEmbed = new EmbedBuilder()
                    .setColor(0x00FF00)
                    .setTitle('✅ Playlist Added')
                    .setDescription(`**${searchResult.playlist.title}**`)
                    .addFields(
                        { name: 'Tracks Added', value: `${searchResult.tracks.length}`, inline: true },
                        { name: 'Source', value: searchResult.playlist.source || 'YouTube', inline: true },
                        { name: 'Total Duration', value: searchResult.playlist.estimatedDuration || 'Unknown', inline: true }
                    )
                    .setThumbnail(searchResult.playlist.thumbnail || searchResult.tracks[0]?.thumbnail)
                    .setFooter({ text: `Requested by ${interaction.user.tag}` })
                    .setTimestamp();

                await interaction.editReply({ embeds: [playlistEmbed] });
            } else {
                const track = searchResult.tracks[0];
                queue.addTrack(track);
                if (!queue.isPlaying()) await queue.node.play();

                const embed = new EmbedBuilder()
                    .setColor(0x00FF00)
                    .setTitle('✅ Track Added')
                    .setDescription(`[${track.title}](${track.url})`)
                    .addFields(
                        { name: 'Duration', value: track.duration, inline: true },
                        { name: 'Source', value: track.source || 'Unknown', inline: true }
                    )
                    .setThumbnail(track.thumbnail)
                    .setFooter({ text: `Requested by ${interaction.user.tag}` });

                await interaction.editReply({ embeds: [embed] });
            }

        } catch (error) {
            console.error('Error in play command:', error);

            // More detailed error messages based on the error type
            let errorMessage = '❌ | An error occurred while trying to play the song.';

            if (error.message && error.message.includes('Could not extract stream')) {
                errorMessage = '❌ | Could not extract audio stream. This track might be unavailable or restricted.';
            } else if (error.message && error.message.includes('No supported transcodings found')) {
                errorMessage = '❌ | This track format is not supported. Try a different track.';
            } else if (error.message && error.message.includes('Sign in to confirm your age')) {
                errorMessage = '❌ | Age-restricted content. Cannot play this track.';
            } else if (error.message && error.message.includes('no audio tracks found')) {
                errorMessage = '❌ | No audio tracks found in this content. Try a different URL or search term.';
            }

            await interaction.editReply({
                content: `${errorMessage}\nUse /diagnose to check player status.`,
                ephemeral: true
            });
        }
    },
};
