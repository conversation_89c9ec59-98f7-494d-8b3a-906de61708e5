// File: src/index.js
// Main entry point for the bot

require('dotenv').config(); // Loads environment variables from .env file
const fs = require('node:fs');
const path = require('node:path');
const { Client, GatewayIntentBits, Collection, Events } = require('discord.js');
const { Player } = require('discord-player');

// Create a new Client instance
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildVoiceStates,
        // GatewayIntentBits.GuildMessages, // Add if you need to read messages
        // GatewayIntentBits.MessageContent, // Add if you need message content
    ],
});

// Import play-dl for better YouTube support
require('play-dl');

// Import YoutubeiExtractor from discord-player-youtubei
const { YoutubeiExtractor } = require('discord-player-youtubei');

// Initialize discord-player with safe optimizations
client.player = new Player(client, {
    ytdlOptions: {
        quality: 'highestaudio', // Always get best quality
        highWaterMark: 1 << 25, // 32MB buffer for smooth streaming
        requestOptions: {
            headers: {
                cookie: process.env.YOUTUBE_COOKIE || ''
            }
        }
    },
    connectionTimeout: 30000, // 30 second timeout
    skipFFmpeg: false, // Keep FFmpeg for filters
    // Safe performance optimizations
    lagMonitor: 2000, // Monitor lag every 2 seconds (less frequent)
    useLegacyFFmpeg: false // Use modern FFmpeg features
});

// Main async function to setup and start the bot
async function main() {

    // Load extractors with improved error handling
    console.log('[Player Extractors] Loading extractors...');

    try {
        // Register the stable YoutubeiExtractor from discord-player-youtubei ONLY
        try {
            await client.player.extractors.register(YoutubeiExtractor, {
                // Configuration for better stability
                streamOptions: {
                    useClient: 'ANDROID', // Use Android client for better compatibility
                    quality: 'best', // Get best quality available
                },
                // Enable caching for better performance
                cache: true
            });
            console.log('[Player Extractors] ✅ Registered stable YoutubeiExtractor with optimized config');
        } catch (err) {
            console.warn(`[Player Extractors] ⚠️ Failed to register YoutubeiExtractor: ${err.message}`);
            // Try with basic config as fallback
            try {
                await client.player.extractors.register(YoutubeiExtractor, {});
                console.log('[Player Extractors] ✅ Registered YoutubeiExtractor with basic config');
            } catch (fallbackErr) {
                console.error(`[Player Extractors] ❌ Failed to register YoutubeiExtractor even with basic config: ${fallbackErr.message}`);
            }
        }

        // Load only non-YouTube default extractors to avoid conflicts
        try {
            const { DefaultExtractors } = require('@discord-player/extractor');

            if (Array.isArray(DefaultExtractors) && DefaultExtractors.length > 0) {
                for (const Extractor of DefaultExtractors) {
                    try {
                        // Skip the unstable YouTube extractor to avoid conflicts
                        const extractorName = Extractor.name || 'Unknown';
                        if (extractorName.toLowerCase().includes('youtube')) {
                            console.log(`[Player Extractors] ⏭️ Skipping ${extractorName} (using stable youtubei instead)`);
                            continue;
                        }

                        await client.player.extractors.register(Extractor);
                        console.log(`[Player Extractors] ✅ Registered extractor: ${extractorName}`);
                    } catch (err) {
                        console.warn(`[Player Extractors] ⚠️ Failed to register extractor: ${err.message}`);
                    }
                }
                console.log('[Player Extractors] ✅ Selective extractor registration complete');
            } else {
                console.log('[Player Extractors] ⚠️ No default extractors found in the module');
            }
        } catch (err) {
            console.warn(`[Player Extractors] ⚠️ Could not load @discord-player/extractor: ${err.message}`);
            console.warn('[Player Extractors] ℹ️ Music functionality may be limited');
        }

        // Check for required dependencies
        try {
            require('mediaplex');
            console.log('[Player Extractors] ✅ mediaplex is installed');
        } catch (err) {
            console.warn('[Player Extractors] ⚠️ mediaplex is not installed. Audio processing may not work properly');
            console.warn('[Player Extractors] ℹ️ Try installing it with: npm install mediaplex');
        }

        try {
            require('ffmpeg-static');
            console.log('[Player Extractors] ✅ ffmpeg-static is installed');
        } catch (err) {
            console.warn('[Player Extractors] ⚠️ ffmpeg-static is not installed. Audio conversion may not work properly');
            console.warn('[Player Extractors] ℹ️ Try installing it with: npm install ffmpeg-static');
        }

        // Log the number of registered extractors
        console.log(`[Player Extractors] 📊 Extractor loading process completed.`);
    } catch (error) {
        console.error('[Player Extractors] ❌ Error setting up player:', error.message);
        console.error('[Player Extractors] ⚠️ Music functionality may not work properly!');
    }

    // Load player events (important for user feedback)
    const playerEventsPath = path.join(__dirname, 'player-events');

    if (fs.existsSync(playerEventsPath)) {
        const playerEventFiles = fs.readdirSync(playerEventsPath).filter(file => file.endsWith('.js'));

        for (const file of playerEventFiles) {
            const filePath = path.join(playerEventsPath, file);
            try {
                const event = require(filePath);
                if (event.name && event.execute) {
                    if (event.once) {
                        client.player.events.once(event.name, (...args) => event.execute(...args, client));
                    } else {
                        client.player.events.on(event.name, (...args) => event.execute(...args, client));
                    }
                } else {
                    console.log(`[WARNING] Player event at ${filePath} is missing name or execute property.`);
                }
            } catch (error) {
                console.error(`[ERROR] Failed to load player event ${file}:`, error.message);
            }
        }
        console.log(`[Player Events] ✅ Loaded ${playerEventFiles.length} player event(s).`);
    } else {
        console.log('[Player Events] ⚠️ No player-events directory found.');
    }

    // Store commands
    client.commands = new Collection();
    const commandsPath = path.join(__dirname, 'commands');

    // Recursive function to load commands from subdirectories
    function loadCommands(directory) {
        if (!fs.existsSync(directory)) {
            console.log(`[Commands] ⚠️ Commands directory not found: ${directory}`);
            return;
        }

        const commandFiles = fs.readdirSync(directory, { withFileTypes: true });
        for (const file of commandFiles) {
            const filePath = path.join(directory, file.name);
            if (file.isDirectory()) {
                loadCommands(filePath); // Recursively load commands in subdirectories
            } else if (file.name.endsWith('.js')) {
                try {
                    const command = require(filePath);
                    if ('data' in command && 'execute' in command) {
                        client.commands.set(command.data.name, command);
                        console.log(`[Commands] ✅ Loaded: ${command.data.name}`);
                    } else {
                        console.log(`[WARNING] The command at ${filePath} is missing a required "data" or "execute" property.`);
                    }
                } catch (error) {
                    console.error(`[ERROR] Failed to load command ${file.name}:`, error.message);
                }
            }
        }
    }

    loadCommands(commandsPath);
    console.log(`[Commands] 📊 Total commands loaded: ${client.commands.size}`);

    // Load Discord client events
    const eventsPath = path.join(__dirname, 'events');

    if (fs.existsSync(eventsPath)) {
        const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

        for (const file of eventFiles) {
            const filePath = path.join(eventsPath, file);
            try {
                const event = require(filePath);
                if (event.name && event.execute) {
                    if (event.once) {
                        client.once(event.name, (...args) => event.execute(...args, client));
                    } else {
                        client.on(event.name, (...args) => event.execute(...args, client));
                    }
                } else {
                    console.log(`[WARNING] Client event at ${filePath} is missing name or execute property.`);
                }
            } catch (error) {
                console.error(`[ERROR] Failed to load client event ${file}:`, error.message);
            }
        }
        console.log(`[Client Events] ✅ Loaded ${eventFiles.length} client event(s).`);
    } else {
        console.log('[Client Events] ⚠️ No events directory found.');
    }

    // Log in to Discord with your client's token
    try {
        console.log('[Login] 🔄 Attempting to log in...');
        await client.login(process.env.BOT_TOKEN);
        console.log('[Login] ✅ Successfully logged in as ' + (client.user ? client.user.tag : 'Unknown User'));
    } catch (err) {
        console.error('[Login Error] ❌ Could not log in:', err.message);
        console.error('[Login Error] Please check your BOT_TOKEN in the .env file');
        process.exit(1);
    }
}

// Enhanced error handling for the main function
main().catch(error => {
    console.error('[Unhandled Main Error] ❌ An error occurred during bot initialization:');
    console.error(error);
    process.exit(1);
});