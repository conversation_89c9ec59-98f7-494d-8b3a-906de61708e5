// File: src/player-events/volumeChange.js
// Event handler for volume changes

module.exports = {
    name: 'volumeChange',
    async execute(queue, oldVolume, newVolume) {
        console.log(`[VolumeChange] Guild: ${queue.guild.name}, Volume changed from ${oldVolume}% to ${newVolume}%`);
        
        // Check if the queue is still playing after volume change
        if (!queue.isPlaying() && queue.tracks.size > 0) {
            console.warn(`[VolumeChange] Queue stopped playing after volume change in guild ${queue.guild.name}`);
            
            // Try to resume playback
            try {
                if (queue.node.isPaused()) {
                    console.log(`[VolumeChange] Attempting to resume playback in guild ${queue.guild.name}`);
                    queue.node.resume();
                } else {
                    console.log(`[VolumeChange] Attempting to restart playback in guild ${queue.guild.name}`);
                    await queue.node.play();
                }
            } catch (error) {
                console.error(`[VolumeChange] Failed to resume playback after volume change:`, error);
            }
        }
    }
};
