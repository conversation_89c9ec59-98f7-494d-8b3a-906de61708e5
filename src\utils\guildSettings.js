// File: src/utils/guildSettings.js
// Simple JSON-based storage for guild settings

const fs = require('node:fs');
const path = require('node:path');

class GuildSettings {
    constructor() {
        this.settingsPath = path.join(__dirname, '..', 'data', 'guildSettings.json');
        this.settings = new Map();
        this.loadSettings();
    }

    /**
     * Ensure the data directory exists
     */
    ensureDataDirectory() {
        const dataDir = path.dirname(this.settingsPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
    }

    /**
     * Load settings from JSON file
     */
    loadSettings() {
        try {
            this.ensureDataDirectory();
            if (fs.existsSync(this.settingsPath)) {
                const data = fs.readFileSync(this.settingsPath, 'utf8');
                const parsed = JSON.parse(data);
                this.settings = new Map(Object.entries(parsed));
                console.log(`[GuildSettings] Loaded settings for ${this.settings.size} guilds`);
            } else {
                console.log('[GuildSettings] No existing settings file found, starting fresh');
            }
        } catch (error) {
            console.error('[GuildSettings] Error loading settings:', error.message);
            this.settings = new Map();
        }
    }

    /**
     * Save settings to JSON file
     */
    saveSettings() {
        try {
            this.ensureDataDirectory();
            const data = Object.fromEntries(this.settings);
            fs.writeFileSync(this.settingsPath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('[GuildSettings] Error saving settings:', error.message);
        }
    }

    /**
     * Get guild settings
     * @param {string} guildId - The guild ID
     * @returns {Object} Guild settings object
     */
    getGuildSettings(guildId) {
        if (!this.settings.has(guildId)) {
            // Default settings for new guilds
            const defaultSettings = {
                volume: 80, // Default volume
                lastUpdated: Date.now()
            };
            this.settings.set(guildId, defaultSettings);
            this.saveSettings();
            return defaultSettings;
        }
        return this.settings.get(guildId);
    }

    /**
     * Set volume for a guild
     * @param {string} guildId - The guild ID
     * @param {number} volume - The volume level (0-250)
     */
    setVolume(guildId, volume) {
        const settings = this.getGuildSettings(guildId);
        settings.volume = volume;
        settings.lastUpdated = Date.now();
        this.settings.set(guildId, settings);
        this.saveSettings();
        console.log(`[GuildSettings] Saved volume ${volume}% for guild ${guildId}`);
    }

    /**
     * Get volume for a guild
     * @param {string} guildId - The guild ID
     * @returns {number} The volume level
     */
    getVolume(guildId) {
        const settings = this.getGuildSettings(guildId);
        return settings.volume;
    }

    /**
     * Update guild settings
     * @param {string} guildId - The guild ID
     * @param {Object} newSettings - Settings to update
     */
    updateGuildSettings(guildId, newSettings) {
        const settings = this.getGuildSettings(guildId);
        Object.assign(settings, newSettings);
        settings.lastUpdated = Date.now();
        this.settings.set(guildId, settings);
        this.saveSettings();
    }

    /**
     * Remove guild settings (cleanup)
     * @param {string} guildId - The guild ID
     */
    removeGuildSettings(guildId) {
        if (this.settings.has(guildId)) {
            this.settings.delete(guildId);
            this.saveSettings();
            console.log(`[GuildSettings] Removed settings for guild ${guildId}`);
        }
    }

    /**
     * Get all guild settings (for debugging)
     * @returns {Map} All guild settings
     */
    getAllSettings() {
        return this.settings;
    }

    /**
     * Clean up old guild settings (optional maintenance)
     * @param {number} maxAge - Maximum age in milliseconds (default: 30 days)
     */
    cleanupOldSettings(maxAge = 30 * 24 * 60 * 60 * 1000) {
        const now = Date.now();
        let cleaned = 0;
        
        for (const [guildId, settings] of this.settings.entries()) {
            if (now - settings.lastUpdated > maxAge) {
                this.settings.delete(guildId);
                cleaned++;
            }
        }
        
        if (cleaned > 0) {
            this.saveSettings();
            console.log(`[GuildSettings] Cleaned up ${cleaned} old guild settings`);
        }
        
        return cleaned;
    }
}

// Export a singleton instance
module.exports = new GuildSettings();
