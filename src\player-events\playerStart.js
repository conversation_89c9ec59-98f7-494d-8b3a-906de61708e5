// File: src/player-events/playerStart.js
// Example event: when a track starts playing

const { EmbedBuilder } = require('discord.js');
const { restoreQueueVolume } = require('../utils/queueManager');

module.exports = {
    name: 'playerStart', // Event name from discord-player
    async execute(queue, track) {
        // Ensure volume is properly restored (backup check)
        restoreQueueVolume(queue);

        // queue.metadata contains the channel and client if you set it up in play.js
        if (queue.metadata && queue.metadata.channel) {
            const embed = new EmbedBuilder()
                .setColor(0x00FF00)
                .setTitle('▶️ Now Playing')
                .setDescription(`[${track.title}](${track.url})`)
                .addFields(
                    { name: 'Artist', value: track.author, inline: true },
                    { name: 'Duration', value: `\`${track.duration}\``, inline: true },
                    { name: 'Requested by', value: `${track.requestedBy || 'Unknown'}`, inline: true }
                )
                .setThumbnail(track.thumbnail || queue.metadata.client.user.displayAvatarURL())
                .setTimestamp();

            try {
                 await queue.metadata.channel.send({ embeds: [embed] });
            } catch (error) {
                console.error("Error sending 'Now Playing' message:", error);
            }
        }
        console.log(`[Player] Started playing: ${track.title} in guild ${queue.guild.name}`);
    }
};